# Limitations of the current version

As stated previously, the current version of the Abaqus-SwiftComp GUI has the following limitations:

1. Change part name and edit the composite layup of 1D SG may cause unexpected crash, therefore save your work frequently is highly recommended.
2. When a lot of work is done in the same session of Abaqus, sometimes in the command line window it shows: cannot find Notepad or SwiftComp, or the input line is too long. The user just need to close the current session of Abaqus and also close the command window, then restart the command window and the GUI.
