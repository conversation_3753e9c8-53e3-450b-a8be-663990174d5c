.. Abaqus-SwiftComp Toolset documentation master file, created by
   sphinx-quickstart on Tue Aug 12 16:26:29 2025.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Abaqus-SwiftComp Toolset documentation
======================================

Welcome to the Abaqus-SwiftComp Toolset documentation! This toolkit provides comprehensive
tools for working with Abaqus and SwiftComp simulations.

You can now write documentation using both ``reStructuredText`` and **Markdown** syntax.
See the `MyST-Parser <https://myst-parser.readthedocs.io/>`_ documentation for Markdown features.


.. toctree::
   :maxdepth: 2
   :caption: Contents:

   getting_started
   user_guide
   api_reference


Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`

