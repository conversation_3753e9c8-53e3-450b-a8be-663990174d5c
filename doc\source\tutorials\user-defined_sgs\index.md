# User-defined SGs

Although common SG function provides users a convenient way to build the geometry of the most common models, often users need to build their own models according to the microstructure they are analyzing. Without using the common SG function, this chapter will illustrate how to build 2D square pack microstructure SGs similar to the above common models. Example is not given for 3D SG since the process is exactly the same as creating a 3D part in Abaqus with information of elements, materials and section assignment. The only difference between user-defined model and common SG model is the generation of the SG. The homogenization and dehomogenization steps are the same. So we will only focus on the SG preparation in this chapter.



```{toctree}
:hidden:
:maxdepth: 1

sg_2d_cross-section.md
sg_2d_airfoil.md
sg_2d_arbitrary.md
```
