# API Reference

Complete API reference for the Abaqus-SwiftComp Toolset.

## Core Modules

### abaqus_toolkit.core

The core module provides fundamental classes and functions.

```{eval-rst}
.. automodule:: abaqus_toolkit.core
   :members:
   :undoc-members:
   :show-inheritance:
```

### abaqus_toolkit.analysis

Analysis-related functionality.

```{eval-rst}
.. automodule:: abaqus_toolkit.analysis
   :members:
   :undoc-members:
   :show-inheritance:
```

## Classes

### AbaqusModel

```python
class AbaqusModel:
    """Main class for Abaqus model operations."""
    
    def __init__(self, model_name: str):
        """Initialize the model.
        
        Args:
            model_name: Name of the model
        """
        pass
    
    def create_part(self, name: str, dimensions: tuple):
        """Create a new part.
        
        Args:
            name: Part name
            dimensions: Part dimensions (x, y, z)
            
        Returns:
            Part object
        """
        pass
```

### SwiftCompAnalysis

```python
class SwiftCompAnalysis:
    """Interface for SwiftComp analysis."""
    
    def run_analysis(self, input_file: str) -> dict:
        """Run SwiftComp analysis.
        
        Args:
            input_file: Path to input file
            
        Returns:
            Analysis results dictionary
        """
        pass
```

## Functions

### Utility Functions

```python
def validate_input(input_data: dict) -> bool:
    """Validate input data structure.
    
    Args:
        input_data: Input data dictionary
        
    Returns:
        True if valid, False otherwise
    """
    pass

def export_results(results: dict, format: str = "vtk"):
    """Export analysis results.
    
    Args:
        results: Results dictionary
        format: Output format ("vtk", "csv", "json")
    """
    pass
```

## Constants

```python
# Material property constants
STEEL_PROPERTIES = {
    "E": 200e9,  # Young's modulus (Pa)
    "nu": 0.3,   # Poisson's ratio
    "rho": 7850  # Density (kg/m³)
}

ALUMINUM_PROPERTIES = {
    "E": 70e9,
    "nu": 0.33,
    "rho": 2700
}
```

## Exceptions

```python
class AbaqusToolkitError(Exception):
    """Base exception for toolkit errors."""
    pass

class ModelError(AbaqusToolkitError):
    """Raised when model operations fail."""
    pass

class AnalysisError(AbaqusToolkitError):
    """Raised when analysis fails."""
    pass
```

## Examples

### Basic Usage

```python
from abaqus_toolkit import AbaqusModel, SwiftCompAnalysis

# Create model
model = AbaqusModel("my_model")

# Create part
part = model.create_part("beam", (10, 1, 1))

# Run analysis
analysis = SwiftCompAnalysis()
results = analysis.run_analysis("input.dat")
```

### Advanced Usage

```python
# Custom material properties
custom_material = {
    "E": 150e9,
    "nu": 0.25,
    "rho": 8000
}

# Apply material to part
part.assign_material(custom_material)

# Configure analysis
analysis.set_solver_options({
    "max_iterations": 1000,
    "tolerance": 1e-6
})
```
